/**
 * Defines the `ZydisRegister` enum.
 */
typedef enum ZydisRegister_
{
    ZYDIS_REGISTER_NONE,

    // General purpose registers  8-bit
    ZYDIS_REGISTER_AL,
    ZYDIS_REGISTER_CL,
    ZYDIS_REGISTER_DL,
    ZYDIS_REGISTER_BL,
    ZYDIS_REGISTER_AH,
    ZYDIS_REGISTER_CH,
    ZYDIS_REGISTER_DH,
    ZYDIS_REGISTER_BH,
    ZYDIS_REGISTER_SPL,
    ZYDIS_REGISTER_BPL,
    ZYDIS_REGISTER_SIL,
    ZYDIS_REGISTER_DIL,
    ZYDIS_REGISTER_R8B,
    ZYDIS_REGISTER_R9B,
    ZYDIS_REGISTER_R10B,
    ZYDIS_REGISTER_R11B,
    ZYDIS_REGISTER_R12B,
    ZYDIS_REGISTER_R13B,
    ZYDIS_REGISTER_R14B,
    ZY<PERSON>S_REGISTER_R15B,

    // General purpose registers 16-bit
    ZYDIS_REGISTER_AX,
    ZYDIS_REGISTER_CX,
    ZY<PERSON>S_REGISTER_DX,
    ZYDIS_REGISTER_BX,
    ZYDIS_REGISTER_SP,
    ZYDIS_REGISTER_BP,
    ZYDIS_REGISTER_SI,
    ZYDIS_REGISTER_DI,
    ZYDIS_REGISTER_R8W,
    ZYDIS_REGISTER_R9W,
    ZYDIS_REGISTER_R10W,
    ZYDIS_REGISTER_R11W,
    ZYDIS_REGISTER_R12W,
    ZYDIS_REGISTER_R13W,
    ZYDIS_REGISTER_R14W,
    ZYDIS_REGISTER_R15W,

    // General purpose registers 32-bit
    ZYDIS_REGISTER_EAX,
    ZYDIS_REGISTER_ECX,
    ZYDIS_REGISTER_EDX,
    ZYDIS_REGISTER_EBX,
    ZYDIS_REGISTER_ESP,
    ZYDIS_REGISTER_EBP,
    ZYDIS_REGISTER_ESI,
    ZYDIS_REGISTER_EDI,
    ZYDIS_REGISTER_R8D,
    ZYDIS_REGISTER_R9D,
    ZYDIS_REGISTER_R10D,
    ZYDIS_REGISTER_R11D,
    ZYDIS_REGISTER_R12D,
    ZYDIS_REGISTER_R13D,
    ZYDIS_REGISTER_R14D,
    ZYDIS_REGISTER_R15D,

    // General purpose registers 64-bit
    ZYDIS_REGISTER_RAX,
    ZYDIS_REGISTER_RCX,
    ZYDIS_REGISTER_RDX,
    ZYDIS_REGISTER_RBX,
    ZYDIS_REGISTER_RSP,
    ZYDIS_REGISTER_RBP,
    ZYDIS_REGISTER_RSI,
    ZYDIS_REGISTER_RDI,
    ZYDIS_REGISTER_R8,
    ZYDIS_REGISTER_R9,
    ZYDIS_REGISTER_R10,
    ZYDIS_REGISTER_R11,
    ZYDIS_REGISTER_R12,
    ZYDIS_REGISTER_R13,
    ZYDIS_REGISTER_R14,
    ZYDIS_REGISTER_R15,

    // Floating point legacy registers
    ZYDIS_REGISTER_ST0,
    ZYDIS_REGISTER_ST1,
    ZYDIS_REGISTER_ST2,
    ZYDIS_REGISTER_ST3,
    ZYDIS_REGISTER_ST4,
    ZYDIS_REGISTER_ST5,
    ZYDIS_REGISTER_ST6,
    ZYDIS_REGISTER_ST7,
    ZYDIS_REGISTER_X87CONTROL,
    ZYDIS_REGISTER_X87STATUS,
    ZYDIS_REGISTER_X87TAG,

    // Floating point multimedia registers
    ZYDIS_REGISTER_MM0,
    ZYDIS_REGISTER_MM1,
    ZYDIS_REGISTER_MM2,
    ZYDIS_REGISTER_MM3,
    ZYDIS_REGISTER_MM4,
    ZYDIS_REGISTER_MM5,
    ZYDIS_REGISTER_MM6,
    ZYDIS_REGISTER_MM7,

    // Floating point vector registers 128-bit
    ZYDIS_REGISTER_XMM0,
    ZYDIS_REGISTER_XMM1,
    ZYDIS_REGISTER_XMM2,
    ZYDIS_REGISTER_XMM3,
    ZYDIS_REGISTER_XMM4,
    ZYDIS_REGISTER_XMM5,
    ZYDIS_REGISTER_XMM6,
    ZYDIS_REGISTER_XMM7,
    ZYDIS_REGISTER_XMM8,
    ZYDIS_REGISTER_XMM9,
    ZYDIS_REGISTER_XMM10,
    ZYDIS_REGISTER_XMM11,
    ZYDIS_REGISTER_XMM12,
    ZYDIS_REGISTER_XMM13,
    ZYDIS_REGISTER_XMM14,
    ZYDIS_REGISTER_XMM15,
    ZYDIS_REGISTER_XMM16,
    ZYDIS_REGISTER_XMM17,
    ZYDIS_REGISTER_XMM18,
    ZYDIS_REGISTER_XMM19,
    ZYDIS_REGISTER_XMM20,
    ZYDIS_REGISTER_XMM21,
    ZYDIS_REGISTER_XMM22,
    ZYDIS_REGISTER_XMM23,
    ZYDIS_REGISTER_XMM24,
    ZYDIS_REGISTER_XMM25,
    ZYDIS_REGISTER_XMM26,
    ZYDIS_REGISTER_XMM27,
    ZYDIS_REGISTER_XMM28,
    ZYDIS_REGISTER_XMM29,
    ZYDIS_REGISTER_XMM30,
    ZYDIS_REGISTER_XMM31,

    // Floating point vector registers 256-bit
    ZYDIS_REGISTER_YMM0,
    ZYDIS_REGISTER_YMM1,
    ZYDIS_REGISTER_YMM2,
    ZYDIS_REGISTER_YMM3,
    ZYDIS_REGISTER_YMM4,
    ZYDIS_REGISTER_YMM5,
    ZYDIS_REGISTER_YMM6,
    ZYDIS_REGISTER_YMM7,
    ZYDIS_REGISTER_YMM8,
    ZYDIS_REGISTER_YMM9,
    ZYDIS_REGISTER_YMM10,
    ZYDIS_REGISTER_YMM11,
    ZYDIS_REGISTER_YMM12,
    ZYDIS_REGISTER_YMM13,
    ZYDIS_REGISTER_YMM14,
    ZYDIS_REGISTER_YMM15,
    ZYDIS_REGISTER_YMM16,
    ZYDIS_REGISTER_YMM17,
    ZYDIS_REGISTER_YMM18,
    ZYDIS_REGISTER_YMM19,
    ZYDIS_REGISTER_YMM20,
    ZYDIS_REGISTER_YMM21,
    ZYDIS_REGISTER_YMM22,
    ZYDIS_REGISTER_YMM23,
    ZYDIS_REGISTER_YMM24,
    ZYDIS_REGISTER_YMM25,
    ZYDIS_REGISTER_YMM26,
    ZYDIS_REGISTER_YMM27,
    ZYDIS_REGISTER_YMM28,
    ZYDIS_REGISTER_YMM29,
    ZYDIS_REGISTER_YMM30,
    ZYDIS_REGISTER_YMM31,

    // Floating point vector registers 512-bit
    ZYDIS_REGISTER_ZMM0,
    ZYDIS_REGISTER_ZMM1,
    ZYDIS_REGISTER_ZMM2,
    ZYDIS_REGISTER_ZMM3,
    ZYDIS_REGISTER_ZMM4,
    ZYDIS_REGISTER_ZMM5,
    ZYDIS_REGISTER_ZMM6,
    ZYDIS_REGISTER_ZMM7,
    ZYDIS_REGISTER_ZMM8,
    ZYDIS_REGISTER_ZMM9,
    ZYDIS_REGISTER_ZMM10,
    ZYDIS_REGISTER_ZMM11,
    ZYDIS_REGISTER_ZMM12,
    ZYDIS_REGISTER_ZMM13,
    ZYDIS_REGISTER_ZMM14,
    ZYDIS_REGISTER_ZMM15,
    ZYDIS_REGISTER_ZMM16,
    ZYDIS_REGISTER_ZMM17,
    ZYDIS_REGISTER_ZMM18,
    ZYDIS_REGISTER_ZMM19,
    ZYDIS_REGISTER_ZMM20,
    ZYDIS_REGISTER_ZMM21,
    ZYDIS_REGISTER_ZMM22,
    ZYDIS_REGISTER_ZMM23,
    ZYDIS_REGISTER_ZMM24,
    ZYDIS_REGISTER_ZMM25,
    ZYDIS_REGISTER_ZMM26,
    ZYDIS_REGISTER_ZMM27,
    ZYDIS_REGISTER_ZMM28,
    ZYDIS_REGISTER_ZMM29,
    ZYDIS_REGISTER_ZMM30,
    ZYDIS_REGISTER_ZMM31,

    // Matrix registers
    ZYDIS_REGISTER_TMM0,
    ZYDIS_REGISTER_TMM1,
    ZYDIS_REGISTER_TMM2,
    ZYDIS_REGISTER_TMM3,
    ZYDIS_REGISTER_TMM4,
    ZYDIS_REGISTER_TMM5,
    ZYDIS_REGISTER_TMM6,
    ZYDIS_REGISTER_TMM7,

    // Flags registers
    ZYDIS_REGISTER_FLAGS,
    ZYDIS_REGISTER_EFLAGS,
    ZYDIS_REGISTER_RFLAGS,

    // Instruction-pointer registers
    ZYDIS_REGISTER_IP,
    ZYDIS_REGISTER_EIP,
    ZYDIS_REGISTER_RIP,

    // Segment registers
    ZYDIS_REGISTER_ES,
    ZYDIS_REGISTER_CS,
    ZYDIS_REGISTER_SS,
    ZYDIS_REGISTER_DS,
    ZYDIS_REGISTER_FS,
    ZYDIS_REGISTER_GS,

    // Table registers
    ZYDIS_REGISTER_GDTR,
    ZYDIS_REGISTER_LDTR,
    ZYDIS_REGISTER_IDTR,
    ZYDIS_REGISTER_TR,

    // Test registers
    ZYDIS_REGISTER_TR0,
    ZYDIS_REGISTER_TR1,
    ZYDIS_REGISTER_TR2,
    ZYDIS_REGISTER_TR3,
    ZYDIS_REGISTER_TR4,
    ZYDIS_REGISTER_TR5,
    ZYDIS_REGISTER_TR6,
    ZYDIS_REGISTER_TR7,

    // Control registers
    ZYDIS_REGISTER_CR0,
    ZYDIS_REGISTER_CR1,
    ZYDIS_REGISTER_CR2,
    ZYDIS_REGISTER_CR3,
    ZYDIS_REGISTER_CR4,
    ZYDIS_REGISTER_CR5,
    ZYDIS_REGISTER_CR6,
    ZYDIS_REGISTER_CR7,
    ZYDIS_REGISTER_CR8,
    ZYDIS_REGISTER_CR9,
    ZYDIS_REGISTER_CR10,
    ZYDIS_REGISTER_CR11,
    ZYDIS_REGISTER_CR12,
    ZYDIS_REGISTER_CR13,
    ZYDIS_REGISTER_CR14,
    ZYDIS_REGISTER_CR15,

    // Debug registers
    ZYDIS_REGISTER_DR0,
    ZYDIS_REGISTER_DR1,
    ZYDIS_REGISTER_DR2,
    ZYDIS_REGISTER_DR3,
    ZYDIS_REGISTER_DR4,
    ZYDIS_REGISTER_DR5,
    ZYDIS_REGISTER_DR6,
    ZYDIS_REGISTER_DR7,
    ZYDIS_REGISTER_DR8,
    ZYDIS_REGISTER_DR9,
    ZYDIS_REGISTER_DR10,
    ZYDIS_REGISTER_DR11,
    ZYDIS_REGISTER_DR12,
    ZYDIS_REGISTER_DR13,
    ZYDIS_REGISTER_DR14,
    ZYDIS_REGISTER_DR15,

    // Mask registers
    ZYDIS_REGISTER_K0,
    ZYDIS_REGISTER_K1,
    ZYDIS_REGISTER_K2,
    ZYDIS_REGISTER_K3,
    ZYDIS_REGISTER_K4,
    ZYDIS_REGISTER_K5,
    ZYDIS_REGISTER_K6,
    ZYDIS_REGISTER_K7,

    // Bound registers
    ZYDIS_REGISTER_BND0,
    ZYDIS_REGISTER_BND1,
    ZYDIS_REGISTER_BND2,
    ZYDIS_REGISTER_BND3,
    ZYDIS_REGISTER_BNDCFG,
    ZYDIS_REGISTER_BNDSTATUS,

    // Uncategorized
    ZYDIS_REGISTER_MXCSR,
    ZYDIS_REGISTER_PKRU,
    ZYDIS_REGISTER_XCR0,
    ZYDIS_REGISTER_UIF,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_REGISTER_MAX_VALUE = ZYDIS_REGISTER_UIF,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_REGISTER_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_REGISTER_MAX_VALUE)
} ZydisRegister;
