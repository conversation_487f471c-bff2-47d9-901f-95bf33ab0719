# AutoPatch - PE文件补丁工具

AutoPatch 是一个用于向PE文件注入shellcode的工具，支持两种不同的补丁模式。

## 功能特性

- **模式1**: 使用特征码定位main函数位置进行补丁
- **模式2**: 直接使用PE结构中的EntryPoint位置进行补丁
- 自动处理32位和64位PE文件
- 自动修改重定位表以避免冲突
- 支持命令行参数配置

## 安装依赖

```bash
pip install pefile
```

## 使用方法

### 基本语法

```bash
python autoPatch.py <PE文件路径> <Shellcode文件路径> [-m 模式]
```

### 参数说明

- `PE文件路径`: 要补丁的PE文件路径
- `Shellcode文件路径`: 包含shellcode的二进制文件路径
- `-m, --mode`: 补丁模式 (可选)
  - `1`: 使用特征码定位main函数 (默认)
  - `2`: 使用PE结构中的EntryPoint位置

### 使用示例

#### 模式1 - 特征码定位main函数 (默认)

```bash
python autoPatch.py target.exe shellcode.bin
# 或者显式指定模式1
python autoPatch.py target.exe shellcode.bin -m 1
```

#### 模式2 - 使用EntryPoint位置

```bash
python autoPatch.py target.exe shellcode.bin -m 2
```

## 模式对比

### 模式1 - 特征码定位main函数

**优点:**
- 更精确地定位到main函数入口
- 适合需要在main函数开始处执行代码的场景
- 保持原有的程序启动流程

**缺点:**
- 依赖特征码匹配，可能在某些编译器或版本下失败
- 需要维护特征码数据库 (pe.txt, pe64.txt)
- 处理时间相对较长

**适用场景:**
- 需要在程序主逻辑开始前执行shellcode
- 目标程序使用常见的编译器和运行时

### 模式2 - EntryPoint位置

**优点:**
- 简单直接，不依赖特征码匹配
- 处理速度快
- 适用于所有PE文件
- 在程序最早期执行shellcode

**缺点:**
- 直接覆盖程序入口点，可能影响程序正常启动
- 需要shellcode自行处理程序的正常启动流程

**适用场景:**
- 需要完全控制程序启动流程
- 特征码匹配失败时的备选方案
- 对执行时机要求极早的场景

## 输出文件

补丁后的文件将保存为 `output.exe`。

## 注意事项

1. **备份原文件**: 在使用工具前请备份原始PE文件
2. **权限要求**: 确保有足够的权限读写文件
3. **杀毒软件**: 某些杀毒软件可能会误报，请添加白名单
4. **测试环境**: 建议先在测试环境中验证补丁效果

## 错误处理

- 如果模式1失败（无法找到特征码），会显示 "Cant pattern crt or main!"
- 如果文件读写失败，会显示相应的错误信息
- 程序会返回适当的退出码以便脚本化使用

## 文件结构

```
AutoPatch/
├── autoPatch.py      # 主程序
├── pe.txt           # 32位PE特征码数据库
├── pe64.txt         # 64位PE特征码数据库
├── example_usage.py # 使用示例
└── README.md        # 说明文档
```

## 开发说明

如需扩展功能，主要的函数包括：

- `patch_pe_mode1()`: 模式1实现
- `patch_pe_mode2()`: 模式2实现
- `patch_pe()`: 主入口函数
- `modify_relocation_entries()`: 重定位表处理

## 许可证

请根据您的需要添加适当的许可证信息。
